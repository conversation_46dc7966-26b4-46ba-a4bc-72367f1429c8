# 一种基于源代码相似度分析的原生漏洞挖掘方法
本发明公开了一种基于源代码相似度分析的原生漏洞挖掘方法。该方法通过构建多语言漏洞特征库，采用多视图代码表征技术（Token序列、AST、CFG、DFG、CPG），使用多头注意力机制融合特征，基于Siamese网络学习代码相似性。方法支持跨语言检测，采用局部敏感哈希和分布式计算提高效率，通过二次验证和上下文过滤降低误报率。实验结果表明，该方法在查准率、查全率和处理速度方面显著优于现有技术，能够有效检测代码混淆后的漏洞，支持C、Java、Python等多种编程语言，适用于大规模代码库分析。本发明为软件安全提供了高效、准确的漏洞挖掘解决方案。

# 本发明的技术方案如下

## 步骤1：漏洞特征库构建与特征模式提取

### 1.1 数据收集与预处理
从国家漏洞数据库NVD（National Vulnerability Database）、GitHub等代码托管平台以及软件保障参考数据集SARD（Software Assurance Reference Dataset）收集漏洞代码片段及其对应的修复补丁。对收集到的代码片段进行标准化处理，包括：
- 去除注释和空行
- 统一代码格式和缩进
- 标注CVE编号（Common Vulnerabilities and Exposures，通用漏洞披露）、CWE类型（Common Weakness Enumeration，通用缺陷枚举）、CVSS评分（Common Vulnerability Scoring System，通用漏洞评分系统）等属性

### 1.2 特征模式定义与提取
本发明中的"特征模式"是指从漏洞代码中提取的多维度特征表示，具体包括：
- **Token序列特征**：将代码分解为词法单元（如关键字、标识符、操作符等）的序列
- **抽象语法树（AST）特征**：代码的树形结构表示，反映代码的语法结构
- **控制流图（CFG）特征**：表示程序执行路径的有向图
- **数据流图（DFG）特征**：表示变量定义和使用关系的图结构
- **代码属性图（CPG）特征**：融合AST、CFG和DFG的统一图表示

这些特征模式将在步骤2中通过不同的技术手段进行提取和表征，确保特征库覆盖多种编程语言（C、Java、Python等）和漏洞类型（缓冲区溢出、SQL注入、跨站脚本等）。

## 步骤2：多视图代码表征技术

### 2.1 Token序列向量化（基于CodeBERT模型）
**架构搭建过程**：
- 使用预训练的CodeBERT模型作为基础架构，CodeBERT是基于Transformer架构的双向编码器，专门针对代码理解任务进行优化
- CodeBERT模型包含12层Transformer编码器，每层包含768维隐藏状态，12个注意力头
- 模型参数量约为125M，能够理解代码的语义和语法结构

**训练过程**：
- 在大规模代码语料库上进行预训练，学习代码的通用表示
- 使用掩码语言模型（MLM）和替换标记检测（RTD）任务进行训练

**实际应用过程**：
- 将输入代码通过CodeBERT的分词器转换为Token序列
- 每个Token映射为768维的向量表示
- 通过Transformer编码器生成上下文感知的Token嵌入向量

### 2.2 抽象语法树（AST）生成
**技术说明**：AST是代码的树形结构表示，每个节点代表代码中的一个语法元素（如函数、变量、表达式等）。

**实现过程**：
- 使用Joern解析器（一个开源的代码分析平台）对源代码进行语法分析
- Joern支持多种编程语言，能够生成标准化的AST结构
- 将AST转换为图神经网络可处理的邻接矩阵和节点特征矩阵

### 2.3 控制流图（CFG）构建
**技术说明**：CFG是表示程序所有可能执行路径的有向图，节点表示基本块（连续执行的语句序列），边表示控制流转移。

**构建过程**：
- 识别代码中的基本块（顺序执行的语句组）
- 分析条件语句、循环语句、函数调用等控制结构
- 构建节点间的有向边，表示程序执行的可能路径

### 2.4 数据流图（DFG）提取
**技术说明**：DFG表示变量的定义-使用关系，帮助理解数据在程序中的流动。

**提取过程**：
- 识别变量的定义点（赋值语句）和使用点（读取变量的位置）
- 建立变量定义到使用的依赖关系
- 构建数据依赖图，节点表示变量操作，边表示数据依赖

### 2.5 代码属性图（CPG）融合
**技术说明**：CPG是将AST、CFG和DFG融合的统一图表示，提供代码的完整结构和语义信息。

**融合过程**：
- 以AST为基础结构框架
- 在AST节点间添加CFG边，表示控制流关系
- 在AST节点间添加DFG边，表示数据流关系
- 生成包含语法、控制流和数据流信息的统一图结构

## 步骤3：基于改进Siamese网络的特征融合与相似度测量

### 3.1 改进Siamese网络架构设计
**网络架构说明**：
本发明采用改进的Siamese网络架构，该网络包含以下组件：
- **共享编码器**：处理输入的代码表征，包含多个卷积层和全连接层
- **多头注意力融合模块**：本发明的创新点，用于融合多视图特征
- **相似度计算层**：计算两个代码片段的相似度分数

**多头注意力机制集成**：
多头注意力机制并非Siamese网络的自带结构，而是本发明特别集成的特征融合模块：
- **注意力头数量**：设置8个注意力头，每个头关注不同的特征维度
- **特征维度**：每个注意力头处理96维特征（总共768维）
- **融合机制**：将Token序列、AST、CFG、DFG四种特征通过注意力权重进行加权融合

**网络参数配置**：
- 共享编码器：3层卷积层（卷积核大小3×3，步长1）+ 2层全连接层（隐藏层维度512）
- 多头注意力层：8个注意力头，每头96维，总输出768维
- 相似度计算：使用余弦相似度和欧几里得距离的组合

### 3.2 训练数据对构建
**代码对的具体含义**：
本发明中的"代码对"包含两种类型：
1. **正样本对（相似对）**：
   - 漏洞代码-漏洞代码：具有相同或相似漏洞模式的代码片段
   - 变体漏洞对：同一漏洞的不同实现方式或混淆版本

2. **负样本对（不相似对）**：
   - 漏洞代码-修复代码：原始漏洞代码与其对应的修复版本
   - 漏洞代码-正常代码：漏洞代码与功能相似但无漏洞的正常代码

### 3.3 训练过程
**损失函数**：使用对比损失（Contrastive Loss）：
```
L = (1-Y) * (1/2) * D² + Y * (1/2) * max(0, margin - D)²
```
其中Y=0表示相似对，Y=1表示不相似对，D为欧几里得距离，margin为边界参数（设置为1.0）

**训练策略**：
- 批次大小：32对代码对
- 学习率：初始学习率0.001，使用余弦退火调度
- 训练轮数：100轮，使用早停机制防止过拟合

## 步骤4：跨语言支持与统一表示

### 4.1 语言无关Tokenizer设计
**与CodeBERT的关系说明**：
步骤2中的CodeBERT主要用于单一语言内的Token序列向量化，而步骤4中的语言无关Tokenizer用于跨语言场景：

**技术实现**：
- **字节对编码（BPE）Tokenizer**：使用SentencePiece库训练跨语言的BPE模型
- **词汇表大小**：50,000个子词单元，覆盖多种编程语言的常用模式
- **特殊标记**：添加语言标识符（如[C]、[JAVA]、[PYTHON]）区分不同语言

### 4.2 中间表示转换
**LLVM IR转换**：
- 将C/C++代码编译为LLVM中间表示（Intermediate Representation）
- 使用统一的指令集表示不同语言的语义
- 通过IR级别的分析实现跨语言漏洞检测

### 4.3 跨语言模型训练
**训练数据**：包含C、Java、Python、JavaScript等多种语言的漏洞代码对
**训练目标**：学习语言无关的漏洞模式表示，使模型能够识别不同语言中的相似漏洞

## 步骤5：高效计算与目标代码检索

### 5.1 目标代码定义
**目标代码具体含义**：
目标代码是指待分析的、需要进行漏洞检测的源代码。具体包括：
- 开发中的新代码
- 第三方库代码
- 开源项目代码
- 遗留系统代码

### 5.2 局部敏感哈希（LSH）检索
**技术原理**：LSH是一种近似最近邻搜索技术，能够快速找到与查询向量相似的向量。

**实现细节**：
- **哈希函数族**：使用随机投影LSH，设置128个哈希函数
- **哈希表数量**：构建16个哈希表，提高召回率
- **相似度阈值**：设置0.8作为相似度阈值，筛选候选漏洞代码

**检索过程**：
1. 将目标代码转换为768维特征向量
2. 通过LSH哈希函数计算哈希值
3. 在哈希表中查找具有相同哈希值的候选代码
4. 计算精确相似度，返回最相似的K个候选（K=10）

### 5.3 分布式计算框架
**Apache Spark集成**：
- **任务分割**：将大型代码库按文件或函数级别进行分割
- **并行处理**：每个Spark执行器处理一个代码子集
- **结果聚合**：收集各节点的检测结果，去重并排序

**性能优化**：
- **模型量化**：将32位浮点模型量化为8位整数，减少内存占用
- **模型剪枝**：移除不重要的神经网络连接，提高推理速度
- **批处理**：将多个代码片段组成批次进行并行推理

## 步骤6：基于子图匹配的漏洞检测与精确定位

### 6.1 子图匹配技术详解
**技术原理**：
子图匹配是一种图算法技术，用于在大图中寻找与给定模式图相匹配的子图结构。在本发明中，子图匹配技术用于在目标代码的CPG（代码属性图）中寻找与已知漏洞模式相匹配的代码结构。

**具体实现方法**：
1. **漏洞模式图构建**：
   - 从步骤1构建的漏洞特征库中提取典型漏洞的CPG结构
   - 将漏洞CPG抽象为模式图，包含关键节点（如危险函数调用、变量操作）和边（控制流、数据流关系）
   - 每个漏洞类型对应一个或多个模式图模板

2. **VF2算法应用**：
   - 使用改进的VF2子图同构算法进行精确匹配
   - VF2算法能够处理有向图和带标签的图，适合CPG结构
   - 算法复杂度为O(N!×M)，其中N为模式图节点数，M为目标图节点数

3. **近似匹配策略**：
   - 引入图编辑距离（Graph Edit Distance）进行近似匹配
   - 允许节点和边的插入、删除、替换操作
   - 设置相似度阈值0.85，超过阈值的匹配被认为是潜在漏洞

### 6.2 相似度比较的具体技术内容
**多层次相似度计算**：
本发明的相似度比较确实基于步骤2和步骤3的技术，但在步骤6中进行了进一步的精细化：

1. **结构相似度**（基于步骤2的图结构特征）：
   - AST结构相似度：比较语法树的拓扑结构
   - CFG路径相似度：分析控制流路径的相似性
   - DFG依赖相似度：比较数据依赖关系的匹配程度

2. **语义相似度**（基于步骤3的特征融合）：
   - 使用训练好的Siamese网络计算代码片段的语义向量
   - 通过余弦相似度衡量语义相似性
   - 结合上下文信息进行语义理解

3. **综合相似度计算**：
   ```
   总相似度 = α×结构相似度 + β×语义相似度 + γ×子图匹配度
   ```
   其中α=0.3, β=0.4, γ=0.3为权重参数

### 6.3 漏洞位置精确定位
**定位算法**：
1. **粗粒度定位**：通过LSH检索确定可疑文件和函数
2. **细粒度定位**：使用子图匹配在函数内部定位具体的代码行
3. **上下文分析**：分析漏洞代码的前后文，确定漏洞的影响范围

**定位结果输出**：
- 文件路径和行号
- 漏洞类型和严重程度
- 相似的已知漏洞CVE编号
- 修复建议和参考补丁

## 步骤7：基于多重验证的误报优化

### 7.1 二次分类器设计与实现
**分类器架构**：
本发明采用基于梯度提升决策树（GBDT）的二次分类器，用于验证初步检测结果：

**特征工程**：
- **代码复杂度特征**：圈复杂度、代码行数、函数调用深度
- **上下文特征**：函数签名、变量类型、调用关系
- **历史特征**：代码修改历史、开发者信息、代码审查记录
- **静态分析特征**：编译器警告、代码规范检查结果

**模型参数**：
- 决策树数量：100棵
- 学习率：0.1
- 最大深度：6
- 特征采样率：0.8

**训练数据**：
- 正样本：经人工确认的真实漏洞
- 负样本：初步检测为漏洞但经验证为误报的代码
- 训练集：验证集：测试集 = 7:2:1

### 7.2 动态分析技术
**符号执行引擎**：
采用KLEE符号执行引擎进行动态验证：

1. **路径探索**：
   - 生成覆盖可疑代码路径的测试用例
   - 使用约束求解器（Z3）生成触发漏洞的输入
   - 设置执行时间限制（60秒）和内存限制（1GB）

2. **漏洞触发验证**：
   - 执行生成的测试用例
   - 监控程序运行状态（内存访问、异常抛出）
   - 记录漏洞触发的具体条件和影响

3. **动态污点分析**：
   - 标记外部输入为污点源
   - 跟踪污点数据的传播路径
   - 检测污点数据是否到达敏感操作（如内存写入、系统调用）

### 7.3 代码上下文和安全模式过滤
**上下文分析实现**：

1. **函数级上下文**：
   - **输入验证检查**：检测函数是否包含输入验证逻辑
   - **边界检查**：分析数组访问和缓冲区操作的边界检查
   - **异常处理**：评估错误处理和异常捕获的完整性

2. **调用链上下文**：
   - **调用者分析**：检查调用该函数的上层代码是否已进行安全检查
   - **参数来源追踪**：分析函数参数的来源和可信度
   - **返回值处理**：检查调用者是否正确处理函数返回值

**安全模式库构建**：
建立已知安全编程模式的知识库：

1. **安全API使用模式**：
   - 安全的字符串操作函数（如strncpy替代strcpy）
   - 内存安全分配和释放模式
   - 加密和哈希函数的正确使用方式

2. **防御性编程模式**：
   - 输入验证和清理模式
   - 错误处理和资源清理模式
   - 权限检查和访问控制模式

**过滤算法**：
```python
def filter_false_positives(detection_result, context, security_patterns):
    # 检查是否匹配已知安全模式
    if matches_security_pattern(detection_result.code, security_patterns):
        return False  # 过滤掉误报

    # 检查上下文保护措施
    if has_input_validation(context) and has_boundary_check(context):
        confidence_score *= 0.5  # 降低置信度

    # 综合判断
    return confidence_score > threshold
```

### 7.4 语义感知表征的实现位置
**语义感知表征的具体实现**：
"语义感知的表征"功能主要在以下步骤中实现：

1. **步骤2.1**：CodeBERT模型提供基础的语义理解能力
   - 通过预训练学习代码的语义表示
   - 理解变量名、函数名的语义含义
   - 捕捉代码的上下文语义关系

2. **步骤3.1**：多头注意力机制增强语义感知
   - 融合多视图特征，形成更丰富的语义表示
   - 注意力机制能够关注代码中的关键语义元素
   - 学习不同代码片段之间的语义相似性

3. **步骤7.3**：上下文分析进一步增强语义理解
   - 结合函数调用关系理解代码语义
   - 分析变量的语义角色（输入、输出、中间变量）
   - 理解代码在整个程序中的语义作用

**语义感知的具体表现**：
- **变量语义理解**：识别变量的语义类型（用户输入、文件路径、数据库查询等）
- **函数语义分析**：理解函数的语义功能（数据处理、安全检查、资源管理等）
- **操作语义识别**：识别操作的语义含义（内存操作、网络通信、文件访问等）

### 7.5 反馈学习机制
**开发者反馈集成**：
1. **误报标记系统**：
   - 提供Web界面供开发者标记误报
   - 记录误报的具体原因和上下文信息
   - 建立误报案例数据库

2. **模型增量更新**：
   - 使用标记的误报数据重新训练二次分类器
   - 更新安全模式库，添加新的安全编程模式
   - 调整相似度计算的权重参数

3. **持续改进**：
   - 定期评估模型性能，调整检测阈值
   - 分析误报模式，改进过滤算法
   - 集成新的漏洞类型和检测规则

本发明的有益效果包括：
1.提高检测准确性：多视图特征融合和Transformer嵌入显著提高查准率和查全率；
2.增强抗混淆能力：语义感知的表征使方法能够识别经过混淆的代码；
3.支持跨语言检测：适用于C、Java、Python等多种编程语言；
4.提高处理效率：高效计算策略使方法能够处理大型代码库；
5.降低误报率：通过区分漏洞和修复代码，减少误报警。

