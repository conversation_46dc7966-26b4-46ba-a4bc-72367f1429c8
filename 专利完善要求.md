
# 编写要求
1. 保持当前专利核心内容不变
2. 根据“存在的问题”，对专利技术方案中的各个步骤进行详细说明，确保每个存在的问题都能够在专利技术方案中得到解决
3. 描述每个步骤的模型时，注意区分预先的架构搭建过程、模型的训练过程和实际应用时的分析过程
4. 需要详细说明使用的模型是什么样？参数如何？模型的作用？不同模型如何接口如何一起工作的并详细描述完整的执行过程
5. 每个步骤都需要非常详细，确保外行人也能看得懂
6. 首次涉及/体积到业内通用的技术或名称，需要进行通俗的解释。并详细说明这个技术在当前专利的作用。


## 存在的问题
1. 步骤 1 中的的特征模式具体指什么，它是步骤2中的Token序列、AST、CFG、DFG吗？
2. 步骤 3 中的多头注意力机制是放在Siamese网络架构中使用的吗？多头注意力机制是Siamese网络架构自带的结构吗？若不是的话，需要说明本案所使用的Siamese网络的具体结构
3. 步骤 3 中的代码对具体的含义是什么，漏洞代码-修复代码，或者漏洞代码-漏洞代码组成的一对数据对吗？
4. 步骤 4 中的前面已经使用CodeBERT模型将代码转换为Token序列向量表示了，这里再使用Tokenizer的话，和上面的CodeBERT模型冲突吗？训练的什么模型？
5. 步骤 5 中目标代码具体指什么？是在待分析的代码中进行检索吗？
6. 步骤 6 中的子图匹配技术具体指什么？相似度比较的具体技术内容是上面的步骤2和3吗？请详细说明本案是如何使用子图匹配技术定位漏洞位置的
7. 步骤 7 中的二次分类器具体指什么？动态分析具体指什么？请提供具体的二次分类器或具体的动态分析方法
8. 步骤 7 中的代码上下文和已知安全模式过滤误报具体是如何实现的？
9. 步骤 7 中的“语义感知的表征”这一功能是上述哪一步骤实现的？

